import os
import glob
import cv2
import numpy as np
from pathlib import Path

def kuan_filter(image, window_size=7, cu=0.9):
    """
    使用 KUAN 滤波器对图像进行降噪处理。

    参数:
    image (numpy.ndarray): 输入的单通道灰度图像 (建议为 float64 类型)。
    window_size (int): 滤波器窗口的大小 (必须是奇数)。
    cu (float): 噪声变化系数 (Noise variation coefficient)。
                 对于SAR图像，典型值范围在 0.1 到 0.3 之间。

    返回:
    numpy.ndarray: 经过 KUAN 滤波处理后的图像。
    """
    if window_size % 2 == 0:
        raise ValueError("窗口大小 (window_size) 必须是奇数。")

    # 将图像转换为 float64 以保证计算精度
    img = image.astype(np.float64)

    # 计算噪声方差系数的平方
    cu_sq = cu * cu

    # 使用均值滤波器计算局部均值
    # 使用 padding 来处理图像边界
    local_mean = cv2.blur(img, (window_size, window_size), borderType=cv2.BORDER_REFLECT)

    # 计算局部均值的平方
    local_mean_sq = cv2.blur(img**2, (window_size, window_size), borderType=cv2.BORDER_REFLECT)

    # 计算局部方差
    local_var = local_mean_sq - local_mean**2

    # 计算局部变化系数的平方 (Ci_sq)
    # 为避免除以零，在分母中添加一个极小值 epsilon
    epsilon = 1e-8
    ci_sq = local_var / ((local_mean**2) + epsilon)

    # 计算权重因子 W
    # 当 cu_sq / ci_sq > 1 时，权重应为0，以避免负值
    # 这发生在局部变化远小于噪声变化时，意味着该区域非常平滑
    w_numerator = 1.0 - (cu_sq / (ci_sq + epsilon))
    w_numerator[w_numerator < 0] = 0
    weight = w_numerator / (1.0 + cu_sq)

    # 应用 KUAN 滤波公式
    # filtered_image = local_mean + weight * (original_image - local_mean)
    filtered_image = local_mean + weight * (img - local_mean)

    # 将图像值裁剪到有效的显示范围 [0, 255] 并转换类型
    filtered_image = np.clip(filtered_image, 0, 255)
    filtered_image = filtered_image.astype(image.dtype)

    return filtered_image

def otsu_thresholding(image):
    """
    使用Otsu算法对输入的灰度图像进行阈值分割。

    参数:
    image (numpy.ndarray): 输入的单通道灰度图像。
                         该图像应为经过降噪处理后的SAR图像。
                         数据类型应为 8-bit 无符号整数 (uint8)。

    返回:
    tuple: 包含两个元素的元组
        - optimal_threshold (float): Otsu算法计算出的最佳阈值。
        - segmented_image (numpy.ndarray): 应用阈值后得到的二值化图像（像素值为0或255）。
    """
    # 检查输入图像是否为8位灰度图，这是Otsu在OpenCV中工作的要求
    if image.dtype != np.uint8:
        print("警告：图像非uint8类型，将进行转换。可能会有精度损失。")
        # 将图像归一化到0-255范围并转换类型
        image = cv2.normalize(image, None, 0, 255, cv2.NORM_MINMAX).astype('uint8')

    # 使用OpenCV的threshold函数进行Otsu阈值分割
    optimal_threshold, segmented_image = cv2.threshold(
        image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU
    )

    return optimal_threshold, segmented_image

def process_images_in_folder(input_folder, filtered_folder, segmented_folder,
                           window_size=7, cu=0.95):
    """
    处理文件夹中的所有.tif格式图像，进行Kuan滤波降噪和OTSU阈值分割。

    参数:
    input_folder (str): 输入图像文件夹路径
    filtered_folder (str): 降噪后图像输出文件夹路径
    segmented_folder (str): 阈值分割后图像输出文件夹路径
    window_size (int): Kuan滤波器窗口大小
    cu (float): Kuan滤波器噪声变化系数
    """
    # 创建输出文件夹
    Path(filtered_folder).mkdir(parents=True, exist_ok=True)
    Path(segmented_folder).mkdir(parents=True, exist_ok=True)

    # 查找所有.tif文件
    tif_pattern = os.path.join(input_folder, "**", "*.tif")
    tif_files = glob.glob(tif_pattern, recursive=True)

    if not tif_files:
        print(f"在文件夹 '{input_folder}' 中未找到.tif格式的图像文件。")
        return

    print(f"找到 {len(tif_files)} 个.tif文件，开始处理...")

    processed_count = 0
    failed_count = 0

    for tif_file in tif_files:
        try:
            # 获取相对路径和文件名
            rel_path = os.path.relpath(tif_file, input_folder)
            filename = os.path.basename(tif_file)
            filename_no_ext = os.path.splitext(filename)[0]

            print(f"正在处理: {rel_path}")

            # 加载图像
            image = cv2.imread(tif_file, cv2.IMREAD_GRAYSCALE)
            if image is None:
                print(f"  错误：无法加载图像 {tif_file}")
                failed_count += 1
                continue

            # 1. Kuan滤波降噪
            print(f"  应用Kuan滤波器 (窗口大小: {window_size}x{window_size})...")
            filtered_image = kuan_filter(image, window_size=window_size, cu=cu)

            # 保存降噪后的图像
            filtered_output_path = os.path.join(filtered_folder, f"{filename_no_ext}_filtered.tif")
            cv2.imwrite(filtered_output_path, filtered_image)

            # 2. OTSU阈值分割
            print(f"  应用OTSU阈值分割...")
            threshold_value, segmented_image = otsu_thresholding(filtered_image)

            # 保存阈值分割后的图像
            segmented_output_path = os.path.join(segmented_folder, f"{filename_no_ext}_segmented.tif")
            cv2.imwrite(segmented_output_path, segmented_image)

            print(f"  完成处理 - 阈值: {threshold_value:.2f}")
            print(f"    降噪图像保存至: {filtered_output_path}")
            print(f"    分割图像保存至: {segmented_output_path}")

            processed_count += 1

        except Exception as e:
            print(f"  错误：处理文件 {tif_file} 时发生异常: {str(e)}")
            failed_count += 1

    print(f"\n处理完成！")
    print(f"成功处理: {processed_count} 个文件")
    print(f"处理失败: {failed_count} 个文件")

if __name__ == "__main__":
    # 设置输入和输出文件夹路径
    input_folder = r"SARFrame\10Scene\1"  # 包含.tif文件的输入文件夹
    filtered_folder =r"filtered\1"  # 降噪后图像输出文件夹
    segmented_folder = r"segmented\1"  # 阈值分割后图像输出文件夹

    # 设置处理参数
    kuan_window_size = 7  # Kuan滤波器窗口大小
    kuan_cu = 0.95       # Kuan滤波器噪声变化系数

    print("开始批量处理SAR图像...")
    print(f"输入文件夹: {input_folder}")
    print(f"降噪输出文件夹: {filtered_folder}")
    print(f"分割输出文件夹: {segmented_folder}")
    print(f"Kuan滤波参数: 窗口大小={kuan_window_size}, 噪声系数={kuan_cu}")
    print("-" * 50)

    # 执行批量处理
    process_images_in_folder(
        input_folder=input_folder,
        filtered_folder=filtered_folder,
        segmented_folder=segmented_folder,
        window_size=kuan_window_size,
        cu=kuan_cu
    )
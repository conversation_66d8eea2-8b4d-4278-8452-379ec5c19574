import cv2
import numpy as np
import os
import glob
import matplotlib.pyplot as plt

# --- 用户配置区 ---
# !!! 请将此路径修改为您存放32张图像的文件夹路径
IMAGE_FOLDER_PATH = r'segmented\1'
IMAGE_EXTENSION = 'tif'

# --- 新增：连通分量分析的过滤器配置 ---
# 根据您绿色框中目标的大致像素数来设定
# 过滤掉面积过小的噪点
MIN_TARGET_AREA = 15 
# 过滤掉面积过大的伪目标（例如背景变化产生的大斑块）
MAX_TARGET_AREA = 50

# 形态学操作的核大小
MORPH_KERNEL_SIZE = (7, 7)

# --- 核心功能函数 ---

def load_images_from_folder(folder_path, extension):
    """
    从文件夹加载并排序图像序列。
    """
    search_path = os.path.join(folder_path, f'*.{extension}')
    file_paths = sorted(glob.glob(search_path))
    if not file_paths:
        print(f"错误：在文件夹 '{folder_path}' 中未找到任何 '{extension}' 格式的图像。")
        return []
    image_sequence = [cv2.imread(fp, cv2.IMREAD_GRAYSCALE) for fp in file_paths]
    print(f"成功加载 {len(image_sequence)} 张图像。")
    return image_sequence

def create_median_background_model(image_sequence):
    """
    使用时间中值滤波创建动态背景模型。
    """
    if not image_sequence:
        return None
    
    print("正在将图像序列堆叠成3D数组...")
    image_stack = np.stack(image_sequence, axis=0)
    
    print("正在计算时间中值以生成背景模型...")
    median_frame = np.median(image_stack, axis=0).astype(np.uint8)
    
    return median_frame

# --- 主执行程序 ---
if __name__ == '__main__':
    # 1. 加载所有图像帧
    frames = load_images_from_folder(IMAGE_FOLDER_PATH, IMAGE_EXTENSION)
    
    if frames:
        # 2. 创建基于时间中值的背景模型
        background_model = create_median_background_model(frames)
        
        # 3. 选取第一帧进行处理演示
        first_frame = frames[0]
        
        # 4. 对第一帧进行背景减除
        foreground_raw = cv2.absdiff(first_frame, background_model)
        
        # 5. 对背景减除后的结果进行形态学处理
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, MORPH_KERNEL_SIZE)
        opened_foreground = cv2.morphologyEx(foreground_raw, cv2.MORPH_OPEN, kernel, iterations=1)
        foreground_cleaned = cv2.morphologyEx(opened_foreground, cv2.MORPH_CLOSE, kernel, iterations=1)
        
        # 6. 【新增】对形态学处理后的图片进行连通分量分析
        #   - stats: 每个标签的统计信息 [x, y, width, height, area]
        #   - labels: 与输入图像大小相同的矩阵，其中每个连通域被赋予了不同的标签（整数值）
        num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(foreground_cleaned, connectivity=8)
        
        # 创建一个黑色的新图像，用于只显示通过了面积过滤的连通分量
        filtered_components_image = np.zeros_like(foreground_cleaned)
        
        # 从标签1开始循环，因为标签0是背景
        for i in range(1, num_labels):
            area = stats[i, cv2.CC_STAT_AREA]
            
            # 如果斑块的面积在我们的预设范围内，则在新的图像上将它绘制出来
            if MIN_TARGET_AREA < area < MAX_TARGET_AREA:
                # `labels == i` 会创建一个布尔掩码，其形状与原图相同
                # 在掩码为True的位置，将新图像的像素值设为255（白色）
                filtered_components_image[labels == i] = 255
        
        print("\n处理完成，正在显示结果...")

        # 7. 【新】可视化五张关键图像
        plt.figure(figsize=(20, 14)) # 调整画布大小以适应2x3布局
        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False

        # 图1: 第一帧原始图片
        plt.subplot(2, 3, 1)
        plt.imshow(first_frame, cmap='gray')
        plt.title('1. 第一帧原始图片')
        plt.axis('off')
        
        # 图2: 建模后的背景图片
        plt.subplot(2, 3, 2)
        plt.imshow(background_model, cmap='gray')
        plt.title('2. 时间中值背景模型')
        plt.axis('off')
        
        # 图3: 经过背景减除的图片
        plt.subplot(2, 3, 3)
        plt.imshow(foreground_raw, cmap='gray')
        plt.title('3. 背景减除结果')
        plt.axis('off')
        
        # 图4: 背景减除后经过形态学处理的图片
        plt.subplot(2, 3, 4)
        plt.imshow(foreground_cleaned, cmap='gray')
        plt.title('4. 形态学处理后结果')
        plt.axis('off')
        
        # 图5: 经过连通分量分析后的图片
        plt.subplot(2, 3, 5)
        plt.imshow(filtered_components_image, cmap='gray')
        plt.title('5. 连通分量分析后结果 (最终目标)')
        plt.axis('off')
        
        plt.tight_layout()
        plt.show()
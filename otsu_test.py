import cv2
import numpy as np
import matplotlib.pyplot as plt

def otsu_thresholding(image):
    """
    使用Otsu算法对输入的灰度图像进行阈值分割。

    参数:
    image (numpy.ndarray): 输入的单通道灰度图像。
                         该图像应为经过降噪处理后的SAR图像。
                         数据类型应为 8-bit 无符号整数 (uint8)。

    返回:
    tuple: 包含两个元素的元组
        - optimal_threshold (float): Otsu算法计算出的最佳阈值。
        - segmented_image (numpy.ndarray): 应用阈值后得到的二值化图像（像素值为0或255）。
    """
    # 检查输入图像是否为8位灰度图，这是Otsu在OpenCV中工作的要求
    if image.dtype != np.uint8:
        print("警告：图像非uint8类型，将进行转换。可能会有精度损失。")
        # 将图像归一化到0-255范围并转换类型
        image = cv2.normalize(image, None, 0, 255, cv2.NORM_MINMAX).astype('uint8')

    # cv2.threshold是OpenCV的核心函数之一，用于阈值处理
    # 第一个参数: 源图像 (必须是灰度图)
    # 第二个参数: 阈值。这里我们传入0，因为Otsu算法会自动计算最佳阈值
    # 第三个参数: 超过阈值的像素点所赋予的新值 (通常是255，表示白色)
    # 第四个参数: 阈值类型。cv2.THRESH_BINARY + cv2.THRESH_OTSU 表示使用二值化和Otsu算法
    # 函数会返回两个值：计算出的最佳阈值 和 阈值处理后的图像
    optimal_threshold, segmented_image = cv2.threshold(
        image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU
    )
    
    return optimal_threshold, segmented_image

# --- 主执行程序 ---
if __name__ == '__main__':
    # --- 1. 加载降噪后的SAR图像 ---
    # !!! 请将 'kuan_filtered_result.tif' 替换为您降噪后图像的真实文件路径
    # 这里我们假设输入图像是之前KUAN滤波步骤保存的结果
    denoised_image_path = 'kuan_filtered_output.png'
    
    try:
        # 使用OpenCV加载图像，IMREAD_UNCHANGED可以保留图像原始位深
        denoised_sar_image = cv2.imread(denoised_image_path, cv2.IMREAD_UNCHANGED)
        
        if denoised_sar_image is None:
            raise FileNotFoundError(f"无法加载图像文件: {denoised_image_path}")

        print(f"成功加载图像 '{denoised_image_path}', 尺寸: {denoised_sar_image.shape}, 数据类型: {denoised_sar_image.dtype}")

        # 如果图像是多通道的，则转换为灰度图
        if len(denoised_sar_image.shape) > 2 and denoised_sar_image.shape[2] > 1:
            print("图像为多通道，将转换为灰度图。")
            denoised_sar_image = cv2.cvtColor(denoised_sar_image, cv2.COLOR_BGR2GRAY)

    except FileNotFoundError as e:
        print(f"警告: {e}")
        print("将创建一个演示用的双峰灰度图像。")
        # 创建一个人工图像，用于演示Otsu算法
        # 背景
        denoised_sar_image = np.zeros((300, 300), dtype=np.uint8)
        # 添加一些随机噪声
        denoised_sar_image = cv2.randn(denoised_sar_image, 50, 20)
        # 添加一个更亮的前景目标区域
        cv2.circle(denoised_sar_image, (150, 150), 80, (150, 150, 150), -1)
        denoised_sar_image = cv2.GaussianBlur(denoised_sar_image, (35, 35), 0)
        print("已生成演示图像。")

    # --- 2. 应用Otsu阈值分割算法 ---
    print("正在应用Otsu阈值分割算法...")
    threshold_value, binary_image = otsu_thresholding(denoised_sar_image)
    print("分割完成。")
    print(f"Otsu算法计算出的最佳阈值为: {threshold_value:.2f}")

    # --- 3. 可视化结果 ---
    plt.style.use('seaborn-v0_8-dark-palette')
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False   # 用来正常显示负号

    # a) 显示降噪后的原始图像
    ax0 = axes[0]
    im0 = ax0.imshow(denoised_sar_image, cmap='gray')
    ax0.set_title('降噪后的SAR图像')
    ax0.axis('off')
    fig.colorbar(im0, ax=ax0, orientation='horizontal', shrink=0.8)

    # b) 显示图像的灰度直方图和Otsu阈值线
    ax1 = axes[1]
    # np.ravel()将图像矩阵展平为一维数组
    ax1.hist(denoised_sar_image.ravel(), bins=256, range=[0, 256], color='gray')
    # 绘制一条红色的垂直线来表示Otsu计算出的阈值位置
    ax1.axvline(threshold_value, color='r', linestyle='--', linewidth=2)
    ax1.set_title(f'灰度直方图 (Otsu阈值 = {threshold_value:.0f})')
    ax1.set_xlabel('像素灰度值')
    ax1.set_ylabel('像素数量')
    ax1.legend(['Otsu阈值'])

    # c) 显示Otsu分割后的二值图像
    ax2 = axes[2]
    ax2.imshow(binary_image, cmap='gray')
    ax2.set_title('Otsu阈值分割结果')
    ax2.axis('off')

    plt.tight_layout()
    plt.show()

    # --- 4. (可选) 保存分割结果 ---
    output_filename = 'otsu_segmented_output.png'
    cv2.imwrite(output_filename, binary_image)
    print(f"分割后的图像已保存为 '{output_filename}'")
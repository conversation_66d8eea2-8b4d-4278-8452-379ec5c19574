import cv2
import numpy as np
import os
import glob
import matplotlib.pyplot as plt

# --- 用户配置区 ---
# !!! 请将此路径修改为您存放32张图像的文件夹路径
IMAGE_FOLDER_PATH = r'segmented\1' 

# 图像文件的扩展名 (例如 'png', 'tif', 'jpg')
IMAGE_EXTENSION = 'tif'

# --- 核心功能函数 ---

def load_images_from_folder(folder_path, extension):
    """
    从文件夹加载并排序图像序列。
    """
    # 构建文件搜索路径
    search_path = os.path.join(folder_path, f'*.{extension}')
    # 使用glob查找所有匹配的文件
    file_paths = sorted(glob.glob(search_path))
    
    if not file_paths:
        print(f"错误：在文件夹 '{folder_path}' 中未找到任何 '{extension}' 格式的图像。")
        return []
        
    image_sequence = []
    print(f"找到了 {len(file_paths)} 张图像，正在加载...")
    for file_path in file_paths:
        # 以灰度模式加载图像
        img = cv2.imread(file_path, cv2.IMREAD_GRAYSCALE)
        if img is not None:
            image_sequence.append(img)
    
    print("图像加载完成。")
    return image_sequence

def create_static_background_model(image_sequence):
    """
    通过对所有帧进行像素级“逻辑与”操作，创建静态背景模型。
    只有在所有帧中都为白色的像素才会被认为是静态背景。
    """
    if not image_sequence:
        return None
    
    # 初始化一个全白的图像作为起点
    static_model = np.ones_like(image_sequence[0], dtype=np.uint8) * 255
    
    # 逐帧进行“与”操作
    for frame in image_sequence:
        static_model = cv2.bitwise_and(static_model, frame)
        
    return static_model

def detect_and_track_target(image_sequence, static_model):
    """
    在图像序列中检测并追踪目标，返回轨迹和带注释的图像。
    """
    target_trajectory = []
    annotated_frames = []
    
    # 定义形态学操作的核
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
    
    for frame in image_sequence:
        # 1. 从当前帧中减去静态背景，得到前景
        foreground = cv2.absdiff(frame, static_model)
        
        # 2. 后处理：使用开运算去除小的噪声点
        cleaned_foreground = cv2.morphologyEx(foreground, cv2.MORPH_OPEN, kernel, iterations=1)
        
        # 3. 查找前景物体的轮廓
        contours, _ = cv2.findContours(cleaned_foreground, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 创建一个彩色的版本用于绘制
        annotated_frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)
        
        # 4. 过滤并定位目标
        found_target = False
        for cnt in contours:
            # 通过面积过滤掉非常小的噪声轮廓
            area = cv2.contourArea(cnt)
            if area > 10:  # 面积阈值，您可以根据目标大小进行调整
                x, y, w, h = cv2.boundingRect(cnt)
                
                # 在彩色图像上绘制绿色的外接矩形
                cv2.rectangle(annotated_frame, (x, y), (x + w, y + h), (0, 255, 0), 2)
                
                # 计算并记录质心作为轨迹点
                center_x = x + w // 2
                center_y = y + h // 2
                target_trajectory.append((center_x, center_y))
                found_target = True
                
                # 在图像上标记中心点
                cv2.circle(annotated_frame, (center_x, center_y), 3, (0, 0, 255), -1)
                break # 假设每帧只有一个目标
        
        # 如果当前帧未检测到目标，可以插入一个标记（可选）
        if not found_target:
            target_trajectory.append(None)

        annotated_frames.append(annotated_frame)
        
    return target_trajectory, annotated_frames

# --- 主执行程序 ---
if __name__ == '__main__':
    # 1. 加载所有图像帧
    frames = load_images_from_folder(IMAGE_FOLDER_PATH, IMAGE_EXTENSION)
    
    if frames:
        # 2. 创建静态背景模型
        background_model = create_static_background_model(frames)
        
        # 3. 执行检测与追踪
        trajectory, annotated_frames_list = detect_and_track_target(frames, background_model)
        
        print("\n--- 检测完成 ---")
        valid_points = [p for p in trajectory if p is not None]
        print(f"在 {len(frames)} 帧中，有 {len(valid_points)} 帧检测到了目标。")
        print("目标运动轨迹（中心点坐标）:", valid_points)

        # 4. 结果可视化
        # 绘制最终的轨迹到最后一帧上
        final_frame_with_trajectory = annotated_frames_list[-1].copy()
        if len(valid_points) > 1:
            # 将轨迹点连接成线
            trajectory_points = np.array(valid_points, dtype=np.int32)
            cv2.polylines(final_frame_with_trajectory, [trajectory_points], isClosed=False, color=(255, 0, 0), thickness=2)

        # 使用 Matplotlib 显示关键图像
        plt.figure(figsize=(18, 7))
        plt.rcParams['font.sans-serif'] = ['SimHei']
        
        # 显示第一帧原始图像
        plt.subplot(1, 3, 1)
        plt.imshow(frames[0], cmap='gray')
        plt.title('第一帧原始图像')
        plt.axis('off')
        
        # 显示生成的背景模型
        plt.subplot(1, 3, 2)
        plt.imshow(background_model, cmap='gray')
        plt.title('生成的静态背景模型')
        plt.axis('off')
        
        # 显示最后一帧的检测结果和完整轨迹
        plt.subplot(1, 3, 3)
        plt.imshow(cv2.cvtColor(final_frame_with_trajectory, cv2.COLOR_BGR2RGB))
        plt.title('最终检测结果与运动轨迹')
        plt.axis('off')
        
        plt.tight_layout()
        plt.show()
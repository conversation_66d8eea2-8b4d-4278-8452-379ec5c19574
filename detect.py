import cv2
import numpy as np
import os
import glob
from pathlib import Path

# 尝试导入matplotlib，如果失败则禁用可视化功能
try:
    import matplotlib.pyplot as plt
    MATPLOTLIB_AVAILABLE = True
except ImportError as e:
    print(f"警告：无法导入matplotlib ({e})，可视化功能将被禁用。")
    MATPLOTLIB_AVAILABLE = False

def load_images_from_folder(folder_path, extension='tif'):
    """
    从文件夹加载并排序图像序列。

    参数:
    folder_path (str): 图像文件夹路径
    extension (str): 图像文件扩展名

    返回:
    list: 加载的图像序列
    """
    # 构建文件搜索路径
    search_path = os.path.join(folder_path, f'*.{extension}')
    # 使用glob查找所有匹配的文件，并按文件名排序
    file_paths = sorted(glob.glob(search_path))

    if not file_paths:
        print(f"错误：在文件夹 '{folder_path}' 中未找到任何 '{extension}' 格式的图像。")
        return []

    image_sequence = []
    print(f"找到了 {len(file_paths)} 张图像，正在加载...")

    for i, file_path in enumerate(file_paths):
        # 以灰度模式加载图像
        img = cv2.imread(file_path, cv2.IMREAD_GRAYSCALE)
        if img is not None:
            image_sequence.append(img)
            print(f"  加载第 {i+1} 张图像: {os.path.basename(file_path)}")
        else:
            print(f"  警告：无法加载图像 {file_path}")

    print(f"图像加载完成，共加载 {len(image_sequence)} 张图像。")
    return image_sequence

def create_static_background_model(image_sequence, method='bitwise_and'):
    """
    创建静态背景模型。

    参数:
    image_sequence (list): 图像序列
    method (str): 背景建模方法 ('bitwise_and', 'median', 'mean')

    返回:
    numpy.ndarray: 静态背景模型
    """
    if not image_sequence:
        return None

    print(f"正在使用 '{method}' 方法创建静态背景模型...")

    if method == 'bitwise_and':
        # 通过对所有帧进行像素级"逻辑与"操作，创建静态背景模型
        # 只有在所有帧中都为白色的像素才会被认为是静态背景
        static_model = np.ones_like(image_sequence[0], dtype=np.uint8) * 255

        for frame in image_sequence:
            static_model = cv2.bitwise_and(static_model, frame)

    elif method == 'median':
        # 使用中值滤波创建背景模型
        image_stack = np.stack(image_sequence, axis=2)
        static_model = np.median(image_stack, axis=2).astype(np.uint8)

    elif method == 'mean':
        # 使用均值创建背景模型
        image_stack = np.stack(image_sequence, axis=2)
        static_model = np.mean(image_stack, axis=2).astype(np.uint8)

    else:
        raise ValueError(f"不支持的背景建模方法: {method}")

    print("静态背景模型创建完成。")
    return static_model

def detect_moving_targets(image_sequence, static_model, min_area=10, max_area=10000):
    """
    在图像序列中检测动目标。

    参数:
    image_sequence (list): 图像序列
    static_model (numpy.ndarray): 静态背景模型
    min_area (int): 最小目标面积阈值
    max_area (int): 最大目标面积阈值

    返回:
    tuple: (目标轨迹列表, 带注释的图像列表, 检测统计信息)
    """
    target_trajectories = []  # 存储所有检测到的目标轨迹
    annotated_frames = []
    detection_stats = []

    # 定义形态学操作的核
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))

    print("开始动目标检测...")

    for frame_idx, frame in enumerate(image_sequence):
        print(f"  处理第 {frame_idx + 1}/{len(image_sequence)} 帧...")

        # 1. 从当前帧中减去静态背景，得到前景
        foreground = cv2.absdiff(frame, static_model)

        # 2. 二值化处理（如果需要）
        _, foreground_binary = cv2.threshold(foreground, 50, 255, cv2.THRESH_BINARY)

        # 3. 形态学操作：先开运算去除噪声，再闭运算填充空洞
        cleaned_foreground = cv2.morphologyEx(foreground_binary, cv2.MORPH_OPEN, kernel, iterations=1)
        cleaned_foreground = cv2.morphologyEx(cleaned_foreground, cv2.MORPH_CLOSE, kernel, iterations=1)

        # 4. 查找前景物体的轮廓
        contours, _ = cv2.findContours(cleaned_foreground, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 创建一个彩色的版本用于绘制
        annotated_frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)

        # 5. 过滤并定位目标
        frame_targets = []
        for cnt in contours:
            # 通过面积过滤轮廓
            area = cv2.contourArea(cnt)
            if min_area <= area <= max_area:
                # 计算外接矩形
                x, y, w, h = cv2.boundingRect(cnt)

                # 计算质心
                M = cv2.moments(cnt)
                if M["m00"] != 0:
                    center_x = int(M["m10"] / M["m00"])
                    center_y = int(M["m01"] / M["m00"])
                else:
                    center_x = x + w // 2
                    center_y = y + h // 2

                # 记录目标信息
                target_info = {
                    'frame': frame_idx,
                    'center': (center_x, center_y),
                    'bbox': (x, y, w, h),
                    'area': area,
                    'contour': cnt
                }
                frame_targets.append(target_info)

                # 在图像上绘制检测结果
                cv2.rectangle(annotated_frame, (x, y), (x + w, y + h), (0, 255, 0), 2)
                cv2.circle(annotated_frame, (center_x, center_y), 3, (0, 0, 255), -1)
                cv2.putText(annotated_frame, f'T{len(frame_targets)}',
                           (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)

        target_trajectories.append(frame_targets)
        annotated_frames.append(annotated_frame)

        # 统计信息
        detection_stats.append({
            'frame': frame_idx,
            'num_targets': len(frame_targets),
            'total_foreground_pixels': np.sum(cleaned_foreground > 0)
        })

        print(f"    检测到 {len(frame_targets)} 个目标")

    print("动目标检测完成。")
    return target_trajectories, annotated_frames, detection_stats

def track_targets_across_frames(target_trajectories, max_distance=50):
    """
    跨帧跟踪目标，将不同帧中的检测结果关联成轨迹。

    参数:
    target_trajectories (list): 每帧的目标检测结果
    max_distance (float): 最大关联距离阈值

    返回:
    list: 连续的目标轨迹列表
    """
    print("开始目标轨迹跟踪...")

    tracks = []  # 存储所有轨迹
    next_track_id = 0

    for frame_idx, frame_targets in enumerate(target_trajectories):
        if frame_idx == 0:
            # 第一帧：为每个目标创建新轨迹
            for target in frame_targets:
                track = {
                    'id': next_track_id,
                    'points': [target],
                    'last_frame': frame_idx,
                    'active': True
                }
                tracks.append(track)
                next_track_id += 1
        else:
            # 后续帧：将目标与现有轨迹关联
            unmatched_targets = frame_targets.copy()

            # 为每个活跃轨迹寻找最佳匹配
            for track in tracks:
                if not track['active']:
                    continue

                last_point = track['points'][-1]
                last_center = last_point['center']

                best_match = None
                best_distance = float('inf')

                # 寻找距离最近的目标
                for target in unmatched_targets:
                    distance = np.sqrt((target['center'][0] - last_center[0])**2 +
                                     (target['center'][1] - last_center[1])**2)

                    if distance < max_distance and distance < best_distance:
                        best_match = target
                        best_distance = distance

                # 如果找到匹配，更新轨迹
                if best_match:
                    track['points'].append(best_match)
                    track['last_frame'] = frame_idx
                    unmatched_targets.remove(best_match)
                else:
                    # 如果连续几帧没有匹配，标记轨迹为非活跃
                    if frame_idx - track['last_frame'] > 3:
                        track['active'] = False

            # 为未匹配的目标创建新轨迹
            for target in unmatched_targets:
                track = {
                    'id': next_track_id,
                    'points': [target],
                    'last_frame': frame_idx,
                    'active': True
                }
                tracks.append(track)
                next_track_id += 1

    # 过滤掉太短的轨迹（可能是噪声）
    valid_tracks = [track for track in tracks if len(track['points']) >= 3]

    print(f"轨迹跟踪完成，共生成 {len(valid_tracks)} 条有效轨迹。")
    return valid_tracks

def save_text_results(tracks, detection_stats, output_dir, num_frames):
    """
    保存文本格式的检测结果。

    参数:
    tracks (list): 目标轨迹
    detection_stats (list): 检测统计信息
    output_dir (str): 输出目录
    num_frames (int): 总帧数
    """
    # 保存轨迹信息到文件
    with open(os.path.join(output_dir, 'trajectory_data.txt'), 'w', encoding='utf-8') as f:
        f.write("动目标检测与跟踪结果\n")
        f.write("=" * 50 + "\n\n")

        f.write(f"总帧数: {num_frames}\n")
        f.write(f"检测到的轨迹数: {len(tracks)}\n\n")

        for track in tracks:
            f.write(f"轨迹 ID: {track['id']}\n")
            f.write(f"轨迹长度: {len(track['points'])} 帧\n")
            f.write("轨迹点坐标:\n")

            for point in track['points']:
                f.write(f"  帧 {point['frame']:2d}: 中心({point['center'][0]:3d}, {point['center'][1]:3d}), "
                       f"面积={point['area']:4.0f}\n")
            f.write("\n")

    # 保存检测统计信息
    with open(os.path.join(output_dir, 'detection_stats.txt'), 'w', encoding='utf-8') as f:
        f.write("每帧检测统计\n")
        f.write("=" * 30 + "\n")
        f.write("帧号\t目标数\t前景像素数\n")
        f.write("-" * 30 + "\n")

        for stat in detection_stats:
            f.write(f"{stat['frame']:3d}\t{stat['num_targets']:3d}\t{stat['total_foreground_pixels']:6d}\n")

    print(f"文本结果已保存到 '{output_dir}' 目录。")

def save_opencv_visualization(image_sequence, static_model, tracks, output_dir):
    """
    使用OpenCV保存可视化结果（不依赖matplotlib）。

    参数:
    image_sequence (list): 原始图像序列
    static_model (numpy.ndarray): 静态背景模型
    tracks (list): 目标轨迹
    output_dir (str): 输出目录
    """
    print("使用OpenCV生成可视化结果...")

    # 1. 保存背景模型
    cv2.imwrite(os.path.join(output_dir, 'background_model.png'), static_model)

    # 2. 保存第一帧和最后一帧
    cv2.imwrite(os.path.join(output_dir, 'first_frame.png'), image_sequence[0])
    cv2.imwrite(os.path.join(output_dir, 'last_frame.png'), image_sequence[-1])

    # 3. 创建轨迹可视化图像
    if len(tracks) > 0:
        # 使用最后一帧作为背景
        trajectory_image = cv2.cvtColor(image_sequence[-1], cv2.COLOR_GRAY2BGR)

        # 定义颜色列表（BGR格式）
        colors = [
            (0, 255, 0),    # 绿色
            (255, 0, 0),    # 蓝色
            (0, 0, 255),    # 红色
            (255, 255, 0),  # 青色
            (255, 0, 255),  # 品红色
            (0, 255, 255),  # 黄色
            (128, 0, 128),  # 紫色
            (255, 165, 0),  # 橙色
            (0, 128, 255),  # 浅蓝色
            (128, 255, 0),  # 浅绿色
        ]

        # 绘制每条轨迹
        for i, track in enumerate(tracks):
            if len(track['points']) > 1:
                color = colors[i % len(colors)]
                trajectory_points = [point['center'] for point in track['points']]

                # 绘制轨迹线
                for j in range(1, len(trajectory_points)):
                    cv2.line(trajectory_image, trajectory_points[j-1], trajectory_points[j], color, 2)

                # 标记起点（圆形）
                cv2.circle(trajectory_image, trajectory_points[0], 6, color, -1)
                cv2.circle(trajectory_image, trajectory_points[0], 8, (255, 255, 255), 2)

                # 标记终点（方形）
                end_point = trajectory_points[-1]
                cv2.rectangle(trajectory_image,
                            (end_point[0]-4, end_point[1]-4),
                            (end_point[0]+4, end_point[1]+4),
                            color, -1)
                cv2.rectangle(trajectory_image,
                            (end_point[0]-6, end_point[1]-6),
                            (end_point[0]+6, end_point[1]+6),
                            (255, 255, 255), 2)

                # 添加轨迹ID标签
                label_pos = (trajectory_points[0][0] + 10, trajectory_points[0][1] - 10)
                cv2.putText(trajectory_image, f'T{track["id"]}', label_pos,
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                cv2.putText(trajectory_image, f'T{track["id"]}', label_pos,
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 1)

        # 保存轨迹可视化图像
        cv2.imwrite(os.path.join(output_dir, 'trajectory_visualization.png'), trajectory_image)

        # 4. 创建轨迹对比图（原图 + 轨迹图）
        comparison_image = np.hstack([
            cv2.cvtColor(image_sequence[-1], cv2.COLOR_GRAY2BGR),
            trajectory_image
        ])
        cv2.imwrite(os.path.join(output_dir, 'trajectory_comparison.png'), comparison_image)

    # 5. 保存前景差分示例
    if len(image_sequence) > 10:
        foreground_example = cv2.absdiff(image_sequence[10], static_model)
        cv2.imwrite(os.path.join(output_dir, 'foreground_example_frame10.png'), foreground_example)

    print(f"OpenCV可视化结果已保存到 '{output_dir}' 目录。")

def visualize_results(image_sequence, static_model, annotated_frames, tracks, detection_stats, output_dir='detection_results'):
    """
    可视化检测和跟踪结果。

    参数:
    image_sequence (list): 原始图像序列
    static_model (numpy.ndarray): 静态背景模型
    annotated_frames (list): 带注释的图像序列
    tracks (list): 目标轨迹
    detection_stats (list): 检测统计信息
    output_dir (str): 输出目录
    """
    print("开始结果可视化...")

    # 创建输出目录
    Path(output_dir).mkdir(parents=True, exist_ok=True)

    # 如果matplotlib不可用，使用OpenCV保存可视化结果
    if not MATPLOTLIB_AVAILABLE:
        print("matplotlib不可用，使用OpenCV保存可视化结果。")
        save_opencv_visualization(image_sequence, static_model, tracks, output_dir)
        save_text_results(tracks, detection_stats, output_dir, len(image_sequence))
        return

    # 1. 显示关键结果图像
    plt.figure(figsize=(20, 12))
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    # 第一帧原始图像
    plt.subplot(2, 3, 1)
    plt.imshow(image_sequence[0], cmap='gray')
    plt.title('第一帧原始图像')
    plt.axis('off')

    # 静态背景模型
    plt.subplot(2, 3, 2)
    plt.imshow(static_model, cmap='gray')
    plt.title('静态背景模型')
    plt.axis('off')

    # 前景差分示例（第10帧）
    if len(image_sequence) > 10:
        foreground_example = cv2.absdiff(image_sequence[10], static_model)
        plt.subplot(2, 3, 3)
        plt.imshow(foreground_example, cmap='gray')
        plt.title('前景差分示例（第10帧）')
        plt.axis('off')

    # 检测结果示例
    if len(annotated_frames) > 10:
        plt.subplot(2, 3, 4)
        plt.imshow(cv2.cvtColor(annotated_frames[10], cv2.COLOR_BGR2RGB))
        plt.title('检测结果示例（第10帧）')
        plt.axis('off')

    # 轨迹可视化
    plt.subplot(2, 3, 5)
    plt.imshow(image_sequence[-1], cmap='gray', alpha=0.7)

    # 绘制所有轨迹
    colors = plt.cm.tab10(np.linspace(0, 1, len(tracks)))
    for i, track in enumerate(tracks):
        if len(track['points']) > 1:
            trajectory_points = [point['center'] for point in track['points']]
            x_coords = [p[0] for p in trajectory_points]
            y_coords = [p[1] for p in trajectory_points]

            plt.plot(x_coords, y_coords, 'o-', color=colors[i],
                    linewidth=2, markersize=4, label=f'轨迹 {track["id"]}')

            # 标记起点和终点
            plt.plot(x_coords[0], y_coords[0], 's', color=colors[i], markersize=8)
            plt.plot(x_coords[-1], y_coords[-1], '^', color=colors[i], markersize=8)

    plt.title('目标运动轨迹')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.axis('off')

    # 检测统计
    plt.subplot(2, 3, 6)
    frame_numbers = [stat['frame'] for stat in detection_stats]
    target_counts = [stat['num_targets'] for stat in detection_stats]

    plt.plot(frame_numbers, target_counts, 'b-o', linewidth=2, markersize=4)
    plt.xlabel('帧数')
    plt.ylabel('检测到的目标数量')
    plt.title('每帧检测到的目标数量统计')
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'detection_summary.png'), dpi=300, bbox_inches='tight')
    plt.show()

    # 2. 保存文本结果
    save_text_results(tracks, detection_stats, output_dir, len(image_sequence))

    print(f"可视化结果已保存到 '{output_dir}' 目录。")

def process_scene_folder(scene_folder_path, output_dir='detection_results',
                        background_method='bitwise_and', min_area=10, max_area=10000):
    """
    处理单个场景文件夹中的所有图像，执行动目标检测。

    参数:
    scene_folder_path (str): 场景文件夹路径
    output_dir (str): 输出目录
    background_method (str): 背景建模方法
    min_area (int): 最小目标面积
    max_area (int): 最大目标面积

    返回:
    dict: 处理结果
    """
    print(f"开始处理场景文件夹: {scene_folder_path}")
    print("=" * 60)

    # 1. 加载图像序列
    image_sequence = load_images_from_folder(scene_folder_path)

    if not image_sequence:
        print("未找到有效图像，处理终止。")
        return None

    # 2. 创建静态背景模型
    static_model = create_static_background_model(image_sequence, method=background_method)

    # 3. 检测动目标
    target_trajectories, annotated_frames, detection_stats = detect_moving_targets(
        image_sequence, static_model, min_area=min_area, max_area=max_area)

    # 4. 跟踪目标轨迹
    tracks = track_targets_across_frames(target_trajectories)

    # 5. 可视化结果
    visualize_results(image_sequence, static_model, annotated_frames, tracks,
                     detection_stats, output_dir)

    # 6. 返回处理结果
    results = {
        'scene_path': scene_folder_path,
        'num_frames': len(image_sequence),
        'num_tracks': len(tracks),
        'tracks': tracks,
        'detection_stats': detection_stats,
        'background_model': static_model
    }

    print("场景处理完成！")
    print("=" * 60)

    return results

# --- 主程序 ---
if __name__ == '__main__':
    # 配置参数
    SCENE_FOLDER_PATH = r'segmented\1'  # 场景1的分割图像文件夹路径
    OUTPUT_DIR = 'detection_results'    # 输出目录

    # 检测参数
    BACKGROUND_METHOD = 'bitwise_and'   # 背景建模方法: 'bitwise_and', 'median', 'mean'
    MIN_TARGET_AREA = 20               # 最小目标面积（像素）
    MAX_TARGET_AREA = 5000             # 最大目标面积（像素）

    print("SAR图像动目标检测系统")
    print("=" * 60)
    print(f"场景文件夹: {SCENE_FOLDER_PATH}")
    print(f"输出目录: {OUTPUT_DIR}")
    print(f"背景建模方法: {BACKGROUND_METHOD}")
    print(f"目标面积范围: {MIN_TARGET_AREA} - {MAX_TARGET_AREA} 像素")
    print("=" * 60)

    # 检查输入文件夹是否存在
    if not os.path.exists(SCENE_FOLDER_PATH):
        print(f"错误：场景文件夹 '{SCENE_FOLDER_PATH}' 不存在！")
        print("请检查路径是否正确。")
        exit(1)

    try:
        # 执行动目标检测
        results = process_scene_folder(
            scene_folder_path=SCENE_FOLDER_PATH,
            output_dir=OUTPUT_DIR,
            background_method=BACKGROUND_METHOD,
            min_area=MIN_TARGET_AREA,
            max_area=MAX_TARGET_AREA
        )

        if results:
            print("\n" + "=" * 60)
            print("处理结果摘要:")
            print(f"  处理帧数: {results['num_frames']}")
            print(f"  检测到的轨迹数: {results['num_tracks']}")

            # 显示每条轨迹的详细信息
            for track in results['tracks']:
                track_length = len(track['points'])
                start_frame = track['points'][0]['frame']
                end_frame = track['points'][-1]['frame']
                start_pos = track['points'][0]['center']
                end_pos = track['points'][-1]['center']

                # 计算轨迹总长度
                total_distance = 0
                for i in range(1, len(track['points'])):
                    p1 = track['points'][i-1]['center']
                    p2 = track['points'][i]['center']
                    total_distance += np.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)

                print(f"  轨迹 {track['id']}:")
                print(f"    持续帧数: {track_length} (第{start_frame+1}帧 - 第{end_frame+1}帧)")
                print(f"    起始位置: ({start_pos[0]}, {start_pos[1]})")
                print(f"    结束位置: ({end_pos[0]}, {end_pos[1]})")
                print(f"    轨迹总长度: {total_distance:.1f} 像素")

            print(f"\n详细结果已保存到 '{OUTPUT_DIR}' 目录中。")
            print("=" * 60)
        else:
            print("处理失败，请检查输入数据。")

    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
import numpy as np
import cv2
from skimage.util import random_noise
import matplotlib.pyplot as plt

def kuan_filter(image, window_size=5, cu=0.25):
    """
    使用 KUAN 滤波器对图像进行降噪处理。

    参数:
    image (numpy.ndarray): 输入的单通道灰度图像 (建议为 float64 类型)。
    window_size (int): 滤波器窗口的大小 (必须是奇数)。
    cu (float): 噪声变化系数 (Noise variation coefficient)。
                 对于SAR图像，典型值范围在 0.1 到 0.3 之间。
                 取值取决于传感器的特性和处理级别。
                 对于单视强度图像，理论值为 1.0。
                 对于多视图像，该值会减小。例如，对于4视图像，约为 1/sqrt(4) = 0.5。
                 这里默认 0.25 是一个常用经验值。

    返回:
    numpy.ndarray: 经过 KUAN 滤波处理后的图像。
    """
    if window_size % 2 == 0:
        raise ValueError("窗口大小 (window_size) 必须是奇数。")

    # 将图像转换为 float64 以保证计算精度
    img = image.astype(np.float64)
    
    # 计算噪声方差系数的平方
    cu_sq = cu * cu
    
    # 使用均值滤波器计算局部均值
    # 使用 padding 来处理图像边界
    local_mean = cv2.blur(img, (window_size, window_size), borderType=cv2.BORDER_REFLECT)
    
    # 计算局部均值的平方
    local_mean_sq = cv2.blur(img**2, (window_size, window_size), borderType=cv2.BORDER_REFLECT)
    
    # 计算局部方差
    local_var = local_mean_sq - local_mean**2
    
    # 计算局部变化系数的平方 (Ci_sq)
    # 为避免除以零，在分母中添加一个极小值 epsilon
    epsilon = 1e-8
    ci_sq = local_var / ((local_mean**2) + epsilon)
    
    # 计算权重因子 W
    # 当 cu_sq / ci_sq > 1 时，权重应为0，以避免负值
    # 这发生在局部变化远小于噪声变化时，意味着该区域非常平滑
    w_numerator = 1.0 - (cu_sq / (ci_sq + epsilon))
    w_numerator[w_numerator < 0] = 0
    weight = w_numerator / (1.0 + cu_sq)
    
    # 应用 KUAN 滤波公式
    # filtered_image = local_mean + weight * (original_image - local_mean)
    filtered_image = local_mean + weight * (img - local_mean)
    
    # 将图像值裁剪到有效的显示范围 [0, 255] 并转换类型
    filtered_image = np.clip(filtered_image, 0, 255)
    filtered_image = filtered_image.astype(image.dtype)
    
    return filtered_image

# --- 主执行程序 ---
if __name__ == '__main__':
    # 1. 加载图像
    # 请将 'your_sar_image.jpg' 替换为您的 SAR 图像文件路径
    # 如果没有真实的 SAR 图像，我们将创建一个带有人工噪声的示例图像进行演示
    try:
        # 尝试加载真实图像
        sar_image = cv2.imread(r'SARFrame\10Scene\1\scene1_frame1.tif', cv2.IMREAD_GRAYSCALE)
        if sar_image is None:
            raise FileNotFoundError
        print("成功加载图像。")
    except (FileNotFoundError, IOError):
        print("警告：未找到指定的图像文件。将创建一个带有斑点噪声的测试图像进行演示。")
        # 创建一个测试图像
        test_image = np.zeros((256, 256), dtype=np.uint8)
        test_image[64:192, 64:192] = 180
        # 添加模拟的 SAR 斑点噪声 (乘性噪声)
        # 'speckle' 噪声模式: out = image + n * image，其中 n 是均匀噪声
        sar_image = (random_noise(test_image, mode='speckle', var=0.1) * 255).astype(np.uint8)

    # 2. 设置滤波器参数
    filter_window_size = 7  # 滤波器窗口大小，例如 5x5, 7x7
    noise_coeff = 0.9      # 噪声变化系数

    # 3. 应用 KUAN 滤波器
    print(f"正在应用 KUAN 滤波器，窗口大小为 {filter_window_size}x{filter_window_size}...")
    kuan_filtered_image = kuan_filter(sar_image, window_size=filter_window_size, cu=noise_coeff)
    print("滤波完成。")

    # 4. 显示结果
    plt.figure(figsize=(15, 7))
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False   # 用来正常显示负号

    plt.subplot(1, 2, 1)
    plt.imshow(sar_image, cmap='gray')
    plt.title('原始 (或带噪) SAR 图像')
    plt.axis('off')

    plt.subplot(1, 2, 2)
    plt.imshow(kuan_filtered_image, cmap='gray')
    plt.title(f'KUAN 滤波后图像 (窗口={filter_window_size})')
    plt.axis('off')

    plt.tight_layout()
    plt.show()

    # 5. (可选) 保存结果
    output_filename = 'kuan_filtered_output.png'
    cv2.imwrite(output_filename, kuan_filtered_image)
    print(f"滤波后的图像已保存为 '{output_filename}'")